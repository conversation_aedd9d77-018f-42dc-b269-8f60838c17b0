# -*- coding: utf-8 -*-
"""
批量处理失败记录的高效脚本
"""

from enhanced_time_extractor import EnhancedTimeExtractor
import pandas as pd
import os
from urllib.parse import urlparse
import time

def categorize_records(records):
    """将记录按类型分类"""
    pdf_records = []
    chinese_news_records = []
    international_news_records = []
    other_records = []
    military_records = []
    
    for record in records:
        data_sources = str(record['data_sources'])
        
        # 获取第一个URL进行分类
        first_url = data_sources.split('；')[0].split('\n')[0].strip()
        
        try:
            domain = urlparse(first_url).netloc.lower()
            
            if '.pdf' in first_url.lower():
                pdf_records.append(record)
            elif '.mil' in domain:
                military_records.append(record)
            elif any(chinese_domain in domain for chinese_domain in ['huaxia.com', 'ltn.com.tw', 'rfi.fr', 'zaobao.com.sg']):
                chinese_news_records.append(record)
            elif any(news_domain in domain for news_domain in ['reuters.com', 'bbc.com']):
                international_news_records.append(record)
            else:
                other_records.append(record)
        except:
            other_records.append(record)
    
    return {
        'pdf': pdf_records,
        'international_news': international_news_records,
        'chinese_news': chinese_news_records,
        'other': other_records,
        'military': military_records
    }

def process_category(extractor, records, category_name, max_records=None):
    """处理特定类别的记录"""
    if not records:
        return []
    
    if max_records:
        records = records[:max_records]
    
    print(f"\n=== 处理{category_name} ({len(records)} 条记录) ===")
    
    results = []
    success_count = 0
    
    for i, record in enumerate(records):
        record_id = record['id']
        data_sources = str(record['data_sources'])
        
        print(f"处理 {i+1}/{len(records)}: {record_id}")
        
        # 特殊处理PDF文件
        if category_name == "PDF文件":
            # 直接从URL提取日期，不需要网络请求
            urls = extractor.parse_multiple_urls(data_sources)
            extracted_time = None
            
            for url in urls:
                if '.pdf' in url.lower():
                    extracted_time = extractor.extract_time_from_pdf_url(url)
                    if extracted_time:
                        break
            
            if not extracted_time:
                extracted_time = "无法获取"
        else:
            # 其他类型使用完整处理
            _, extracted_time = extractor.process_record(record_id, data_sources)
        
        if extracted_time and extracted_time != "无法获取":
            success_count += 1
            print(f"✓ 成功: {extracted_time}")
        else:
            print(f"✗ 失败")
        
        record['publish_time'] = extracted_time
        results.append(record)
        
        # 添加延迟避免被封（PDF文件不需要）
        if category_name != "PDF文件":
            time.sleep(2)
    
    success_rate = success_count / len(records) * 100 if records else 0
    print(f"{category_name}处理完成: 成功 {success_count}/{len(records)} ({success_rate:.1f}%)")
    
    return results

def main():
    """主处理函数"""
    # 创建结果目录
    os.makedirs('retry_results', exist_ok=True)
    
    # 读取所有失败记录
    failed_files = [
        'failed_records/东海_extracted_failed.csv',
        'failed_records/南海_extracted_failed.csv', 
        'failed_records/黄渤海_extracted_failed.csv'
    ]
    
    all_records = []
    file_record_map = {}  # 记录每个记录属于哪个文件
    
    for file_path in failed_files:
        if os.path.exists(file_path):
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            file_name = os.path.basename(file_path)
            
            for _, row in df.iterrows():
                all_records.append(row)
                file_record_map[row['id']] = file_name
    
    print(f"总共读取 {len(all_records)} 条失败记录")
    
    # 分类记录
    categorized = categorize_records(all_records)
    
    print("\n=== 记录分类结果 ===")
    for category, records in categorized.items():
        print(f"{category}: {len(records)} 条")
    
    # 创建提取器
    extractor = EnhancedTimeExtractor()
    
    # 按优先级处理各类别
    all_results = []
    
    # 1. 优先处理PDF文件（成功率最高，不需要网络请求）
    pdf_results = process_category(extractor, categorized['pdf'], "PDF文件")
    all_results.extend(pdf_results)
    
    # 2. 处理国际新闻网站（中等成功率）
    intl_results = process_category(extractor, categorized['international_news'], "国际新闻网站", max_records=10)
    all_results.extend(intl_results)
    
    # 3. 处理中文新闻网站
    chinese_results = process_category(extractor, categorized['chinese_news'], "中文新闻网站", max_records=10)
    all_results.extend(chinese_results)
    
    # 4. 处理其他网站（限制数量）
    other_results = process_category(extractor, categorized['other'], "其他网站", max_records=20)
    all_results.extend(other_results)
    
    # 5. 军方网站成功率很低，暂时跳过
    print(f"\n跳过军方网站 {len(categorized['military'])} 条记录（成功率极低）")
    
    # 统计总体结果
    total_processed = len(all_results)
    total_success = sum(1 for r in all_results if r['publish_time'] != "无法获取")
    
    print(f"\n=== 总体处理结果 ===")
    print(f"处理记录数: {total_processed}")
    print(f"成功提取: {total_success}")
    print(f"总体成功率: {total_success / total_processed * 100:.1f}%")
    
    # 保存结果
    if all_results:
        result_df = pd.DataFrame(all_results)
        result_df.to_csv('retry_results/batch_retry_results.csv', index=False, encoding='utf-8-sig')
        print(f"结果已保存到: retry_results/batch_retry_results.csv")
    
    return all_results

if __name__ == "__main__":
    results = main()
