# -*- coding: utf-8 -*-
"""
生成处理报告的脚本
"""

import pandas as pd
import os
from datetime import datetime

def generate_processing_report():
    """生成详细的处理报告"""
    
    print("=== 新闻网站时间提取处理报告 ===")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 原始数据统计
    print("\n1. 原始数据统计")
    print("-" * 30)
    
    original_files = [
        'extracted_data/东海_extracted.csv',
        'extracted_data/南海_extracted.csv',
        'extracted_data/黄渤海_extracted.csv'
    ]
    
    total_original_records = 0
    total_original_success = 0
    total_original_failed = 0
    
    for file_path in original_files:
        if os.path.exists(file_path):
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            file_total = len(df)
            file_success = len(df[df['publish_time'] != '无法获取'])
            file_failed = len(df[df['publish_time'] == '无法获取'])
            
            total_original_records += file_total
            total_original_success += file_success
            total_original_failed += file_failed
            
            filename = os.path.basename(file_path)
            print(f"{filename}:")
            print(f"  总记录数: {file_total}")
            print(f"  成功提取: {file_success}")
            print(f"  提取失败: {file_failed}")
            print(f"  成功率: {file_success / file_total * 100:.1f}%")
    
    print(f"\n总计:")
    print(f"  总记录数: {total_original_records}")
    print(f"  成功提取: {total_original_success}")
    print(f"  提取失败: {total_original_failed}")
    print(f"  总体成功率: {total_original_success / total_original_records * 100:.1f}%")
    
    # 2. 失败记录分析
    print("\n2. 失败记录分析")
    print("-" * 30)
    
    failed_files = [
        'failed_records/东海_extracted_failed.csv',
        'failed_records/南海_extracted_failed.csv',
        'failed_records/黄渤海_extracted_failed.csv'
    ]
    
    total_failed_records = 0
    pdf_count = 0
    military_count = 0
    chinese_news_count = 0
    international_news_count = 0
    other_count = 0
    
    for file_path in failed_files:
        if os.path.exists(file_path):
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            file_failed = len(df)
            total_failed_records += file_failed
            
            # 分类统计
            for _, row in df.iterrows():
                data_sources = str(row['data_sources'])
                first_url = data_sources.split('；')[0].split('\n')[0].strip()
                
                if '.pdf' in first_url.lower():
                    pdf_count += 1
                elif '.mil' in first_url.lower():
                    military_count += 1
                elif any(domain in first_url.lower() for domain in ['huaxia.com', 'ltn.com.tw', 'rfi.fr']):
                    chinese_news_count += 1
                elif any(domain in first_url.lower() for domain in ['reuters.com', 'bbc.com']):
                    international_news_count += 1
                else:
                    other_count += 1
    
    print(f"失败记录总数: {total_failed_records}")
    print(f"  PDF文件: {pdf_count} 条 ({pdf_count / total_failed_records * 100:.1f}%)")
    print(f"  军方网站: {military_count} 条 ({military_count / total_failed_records * 100:.1f}%)")
    print(f"  中文新闻网站: {chinese_news_count} 条 ({chinese_news_count / total_failed_records * 100:.1f}%)")
    print(f"  国际新闻网站: {international_news_count} 条 ({international_news_count / total_failed_records * 100:.1f}%)")
    print(f"  其他网站: {other_count} 条 ({other_count / total_failed_records * 100:.1f}%)")
    
    # 3. 重新处理结果
    print("\n3. 重新处理结果")
    print("-" * 30)
    
    pdf_results_file = 'retry_results/all_pdf_results.csv'
    if os.path.exists(pdf_results_file):
        pdf_df = pd.read_csv(pdf_results_file, encoding='utf-8-sig')
        pdf_processed = len(pdf_df)
        pdf_success = len(pdf_df[pdf_df['publish_time'] != '无法获取'])
        
        print(f"PDF文件重新处理:")
        print(f"  处理记录数: {pdf_processed}")
        print(f"  成功提取: {pdf_success}")
        print(f"  成功率: {pdf_success / pdf_processed * 100:.1f}%")
        
        # 更新后的总体统计
        new_total_success = total_original_success + pdf_success
        new_total_failed = total_original_failed - pdf_success
        
        print(f"\n更新后的总体统计:")
        print(f"  总记录数: {total_original_records}")
        print(f"  成功提取: {new_total_success}")
        print(f"  提取失败: {new_total_failed}")
        print(f"  总体成功率: {new_total_success / total_original_records * 100:.1f}%")
        print(f"  成功率提升: +{pdf_success / total_original_records * 100:.1f}%")
    else:
        print("未找到PDF处理结果文件")
    
    # 4. 技术总结
    print("\n4. 技术总结")
    print("-" * 30)
    print("成功策略:")
    print("  ✓ PDF文件URL日期提取 - 成功率100%")
    print("  ✓ URL格式修复算法 - 解决连字符问题")
    print("  ✓ 多种日期格式解析 - 支持ISO、中文等格式")
    
    print("\n遇到的挑战:")
    print("  ✗ 军方网站访问限制 - 403/401错误")
    print("  ✗ 部分网站反爬虫机制 - 405错误")
    print("  ✗ 网络连接超时问题")
    
    print("\n建议后续策略:")
    print("  • 对于军方网站，可能需要特殊的访问方式或代理")
    print("  • 对于反爬虫网站，需要更复杂的请求头和延迟策略")
    print("  • 考虑使用浏览器自动化工具处理JavaScript渲染的页面")
    
    # 5. 文件清单
    print("\n5. 生成的文件清单")
    print("-" * 30)
    
    generated_files = [
        'enhanced_time_extractor.py',
        'batch_process_failed.py', 
        'process_pdf_records.py',
        'retry_results/all_pdf_results.csv',
        'extracted_data/南海_extracted_backup.csv'
    ]
    
    for file_path in generated_files:
        if os.path.exists(file_path):
            print(f"  ✓ {file_path}")
        else:
            print(f"  ✗ {file_path} (不存在)")
    
    print("\n" + "=" * 60)
    print("报告生成完成")

if __name__ == "__main__":
    generate_processing_report()
