# -*- coding: utf-8 -*-
"""
专门处理PDF记录的脚本 - 成功率最高
"""

from enhanced_time_extractor import EnhancedTimeExtractor
import pandas as pd
import os

def process_all_pdf_records():
    """处理所有PDF记录"""
    
    # 创建结果目录
    os.makedirs('retry_results', exist_ok=True)
    
    # 读取所有失败记录文件
    failed_files = [
        'failed_records/东海_extracted_failed.csv',
        'failed_records/南海_extracted_failed.csv', 
        'failed_records/黄渤海_extracted_failed.csv'
    ]
    
    extractor = EnhancedTimeExtractor()
    
    all_pdf_results = []
    total_pdf_count = 0
    total_success_count = 0
    
    print("=== 处理所有PDF记录 ===\n")
    
    for file_path in failed_files:
        if not os.path.exists(file_path):
            print(f"警告: 文件不存在 {file_path}")
            continue
        
        print(f"处理文件: {file_path}")
        
        # 读取文件
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        # 找出PDF记录
        pdf_records = []
        for _, row in df.iterrows():
            data_sources = str(row['data_sources'])
            if '.pdf' in data_sources.lower():
                pdf_records.append(row)
        
        print(f"  找到 {len(pdf_records)} 个PDF记录")
        
        if not pdf_records:
            continue
        
        # 处理PDF记录
        file_results = []
        file_success = 0
        
        for i, record in enumerate(pdf_records):
            record_id = record['id']
            data_sources = str(record['data_sources'])
            
            print(f"  处理 {i+1}/{len(pdf_records)}: {record_id}")
            
            # 解析URL并提取时间
            urls = extractor.parse_multiple_urls(data_sources)
            extracted_time = None
            
            for url in urls:
                if '.pdf' in url.lower():
                    extracted_time = extractor.extract_time_from_pdf_url(url)
                    if extracted_time and extracted_time != "无法获取":
                        break
            
            if not extracted_time:
                extracted_time = "无法获取"
            
            # 更新记录
            record['publish_time'] = extracted_time
            file_results.append(record)
            
            if extracted_time != "无法获取":
                file_success += 1
                print(f"    ✓ 成功: {extracted_time}")
            else:
                print(f"    ✗ 失败")
        
        # 统计文件结果
        total_pdf_count += len(pdf_records)
        total_success_count += file_success
        all_pdf_results.extend(file_results)
        
        file_success_rate = file_success / len(pdf_records) * 100 if pdf_records else 0
        print(f"  文件处理完成: 成功 {file_success}/{len(pdf_records)} ({file_success_rate:.1f}%)")
        print("-" * 60)
    
    # 保存所有PDF结果
    if all_pdf_results:
        result_df = pd.DataFrame(all_pdf_results)
        result_df.to_csv('retry_results/all_pdf_results.csv', index=False, encoding='utf-8-sig')
        
        print(f"\n=== PDF处理总结 ===")
        print(f"总PDF记录数: {total_pdf_count}")
        print(f"成功提取: {total_success_count}")
        print(f"总体成功率: {total_success_count / total_pdf_count * 100:.1f}%")
        print(f"结果文件: retry_results/all_pdf_results.csv")
        
        return result_df
    else:
        print("没有找到PDF记录")
        return None

def update_original_files_with_pdf_results():
    """将PDF结果更新到原始文件中"""
    
    print("\n=== 更新原始文件 ===")
    
    # 读取PDF处理结果
    pdf_results_file = 'retry_results/all_pdf_results.csv'
    if not os.path.exists(pdf_results_file):
        print("PDF结果文件不存在")
        return
    
    pdf_results_df = pd.read_csv(pdf_results_file, encoding='utf-8-sig')
    print(f"读取到 {len(pdf_results_df)} 条PDF结果")
    
    # 创建ID到时间的映射
    pdf_time_map = {}
    for _, row in pdf_results_df.iterrows():
        if row['publish_time'] != "无法获取":
            pdf_time_map[row['id']] = row['publish_time']
    
    print(f"其中 {len(pdf_time_map)} 条成功提取了时间")
    
    # 更新原始文件
    original_files = [
        'extracted_data/东海_extracted.csv',
        'extracted_data/南海_extracted.csv',
        'extracted_data/黄渤海_extracted.csv'
    ]
    
    total_updated = 0
    
    for file_path in original_files:
        if not os.path.exists(file_path):
            print(f"跳过: {file_path} 不存在")
            continue
        
        print(f"更新文件: {file_path}")
        
        # 读取原始文件
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        original_count = len(df)
        
        # 更新时间
        updated_count = 0
        for index, row in df.iterrows():
            record_id = row['id']
            if record_id in pdf_time_map:
                df.at[index, 'publish_time'] = pdf_time_map[record_id]
                updated_count += 1
        
        # 保存更新后的文件
        if updated_count > 0:
            # 创建备份
            backup_file = file_path.replace('.csv', '_backup.csv')
            df.to_csv(backup_file, index=False, encoding='utf-8-sig')
            
            # 保存更新后的文件
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            print(f"  更新了 {updated_count} 条记录")
            print(f"  备份文件: {backup_file}")
            
            total_updated += updated_count
        else:
            print(f"  没有需要更新的记录")
    
    print(f"\n总共更新了 {total_updated} 条记录")

def main():
    """主函数"""
    # 处理PDF记录
    pdf_results = process_all_pdf_records()
    
    if pdf_results is not None and len(pdf_results) > 0:
        # 更新原始文件
        update_original_files_with_pdf_results()
        
        print(f"\n=== 处理完成 ===")
        print("PDF记录处理成功率很高，建议:")
        print("1. 检查 retry_results/all_pdf_results.csv 确认结果")
        print("2. 检查原始文件是否正确更新")
        print("3. 对于其他类型的记录，可能需要不同的策略")
    else:
        print("没有成功处理任何PDF记录")

if __name__ == "__main__":
    main()
