# -*- coding: utf-8 -*-
import requests
import pandas as pd
from bs4 import BeautifulSoup
import re
from datetime import datetime, timedelta
import time
import json
from urllib.parse import urlparse, urljoin
import random
from typing import List, Dict, Optional, Tuple
import logging
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedTimeExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        self.success_count = 0
        self.failure_count = 0
        self.results = []
        
    def setup_session(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
        ]
        
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5,zh-CN,zh;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def get_random_user_agent(self):
        return random.choice(self.user_agents)
        
    def make_request(self, url: str, timeout: int = 30, retries: int = 3):
        for attempt in range(retries):
            try:
                headers = {'User-Agent': self.get_random_user_agent()}
                
                response = self.session.get(
                    url, 
                    headers=headers, 
                    timeout=timeout,
                    allow_redirects=True,
                    verify=False
                )
                
                if response.status_code == 200:
                    return response
                elif response.status_code in [403, 429]:
                    wait_time = (attempt + 1) * 5
                    logger.warning(f'Request rejected {response.status_code}, waiting {wait_time} seconds')
                    time.sleep(wait_time)
                else:
                    logger.warning(f'HTTP {response.status_code} for {url}')
                    
            except requests.exceptions.Timeout:
                logger.warning(f'Timeout (attempt {attempt + 1}/{retries}): {url}')
            except requests.exceptions.ConnectionError:
                logger.warning(f'Connection error (attempt {attempt + 1}/{retries}): {url}')
            except Exception as e:
                logger.warning(f'Request exception (attempt {attempt + 1}/{retries}): {url} - {e}')
            
            if attempt < retries - 1:
                wait_time = random.uniform(2, 5)
                time.sleep(wait_time)
        
        return None

    def parse_multiple_urls(self, data_sources: str) -> List[str]:
        """解析多个URL"""
        urls = []

        # 处理分号分隔的URL
        if '；' in data_sources:
            urls = [url.strip() for url in data_sources.split('；') if url.strip()]
        # 处理换行分隔的URL
        elif '\n' in data_sources:
            urls = [url.strip() for url in data_sources.split('\n') if url.strip()]
        else:
            urls = [data_sources.strip()]

        # 过滤和修复URL
        valid_urls = []
        for url in urls:
            if url and url != 'nan' and url.startswith(('http://', 'https://')):
                # 修复URL格式问题：将连字符替换为斜杠（除了域名中的连字符）
                fixed_url = self.fix_url_format(url)
                valid_urls.append(fixed_url)

        return valid_urls

    def fix_url_format(self, url: str) -> str:
        """修复URL格式问题"""
        # 检查是否需要修复（URL中没有正常的路径分隔符但有很多连字符）
        if '://' in url and url.count('/') <= 2 and url.count('-') > 5:
            # 分离协议和域名
            protocol, rest = url.split('://', 1)

            # 查找第一个连字符后的部分作为路径
            parts = rest.split('-')
            if len(parts) > 1:
                domain = parts[0]
                path_parts = parts[1:]

                # 重建URL
                fixed_url = f"{protocol}://{domain}/" + '/'.join(path_parts)
                return fixed_url

        return url

    def extract_time_from_html(self, html_content: str, url: str) -> Optional[str]:
        """从HTML内容中提取时间"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 方法1: 查找JSON-LD结构化数据
            json_ld_time = self.extract_from_json_ld(soup)
            if json_ld_time:
                return json_ld_time

            # 方法2: 查找meta标签
            meta_time = self.extract_from_meta_tags(soup)
            if meta_time:
                return meta_time

            # 方法3: 查找time标签
            time_tag_time = self.extract_from_time_tags(soup)
            if time_tag_time:
                return time_tag_time

            # 方法4: 使用正则表达式查找日期模式
            regex_time = self.extract_from_text_patterns(html_content)
            if regex_time:
                return regex_time

            # 方法5: 网站特定的提取规则
            site_specific_time = self.extract_site_specific(soup, url)
            if site_specific_time:
                return site_specific_time

        except Exception as e:
            logger.warning(f"HTML解析错误: {e}")

        return None

    def extract_from_json_ld(self, soup: BeautifulSoup) -> Optional[str]:
        """从JSON-LD结构化数据中提取时间"""
        try:
            scripts = soup.find_all('script', type='application/ld+json')
            for script in scripts:
                try:
                    data = json.loads(script.string)

                    # 处理单个对象
                    if isinstance(data, dict):
                        time_fields = ['datePublished', 'dateCreated', 'dateModified', 'publishedDate']
                        for field in time_fields:
                            if field in data and data[field]:
                                return self.normalize_time_format(data[field])

                    # 处理数组
                    elif isinstance(data, list):
                        for item in data:
                            if isinstance(item, dict):
                                time_fields = ['datePublished', 'dateCreated', 'dateModified', 'publishedDate']
                                for field in time_fields:
                                    if field in item and item[field]:
                                        return self.normalize_time_format(item[field])

                except json.JSONDecodeError:
                    continue

        except Exception as e:
            logger.debug(f"JSON-LD解析错误: {e}")

        return None

    def extract_from_meta_tags(self, soup: BeautifulSoup) -> Optional[str]:
        """从meta标签中提取时间"""
        meta_patterns = [
            {'property': 'article:published_time'},
            {'property': 'article:modified_time'},
            {'name': 'publishdate'},
            {'name': 'date'},
            {'name': 'DC.date'},
            {'name': 'DC.date.created'},
            {'name': 'DC.date.issued'},
            {'property': 'og:updated_time'},
            {'name': 'twitter:data1'},
            {'itemprop': 'datePublished'},
            {'itemprop': 'dateCreated'}
        ]

        for pattern in meta_patterns:
            meta_tag = soup.find('meta', pattern)
            if meta_tag and meta_tag.get('content'):
                time_str = meta_tag.get('content')
                normalized_time = self.normalize_time_format(time_str)
                if normalized_time:
                    return normalized_time

        return None

    def extract_from_time_tags(self, soup: BeautifulSoup) -> Optional[str]:
        """从time标签中提取时间"""
        time_tags = soup.find_all('time')
        for tag in time_tags:
            # 优先使用datetime属性
            if tag.get('datetime'):
                time_str = tag.get('datetime')
                normalized_time = self.normalize_time_format(time_str)
                if normalized_time:
                    return normalized_time

            # 使用标签内容
            if tag.string:
                time_str = tag.string.strip()
                normalized_time = self.normalize_time_format(time_str)
                if normalized_time:
                    return normalized_time

        return None

    def extract_from_text_patterns(self, html_content: str) -> Optional[str]:
        """使用正则表达式从文本中提取日期"""
        # 日期模式（按优先级排序）
        date_patterns = [
            # ISO 8601格式
            r'\b(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?)\b',
            # 标准日期时间格式
            r'\b(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\b',
            # 日期格式
            r'\b(\d{4}-\d{2}-\d{2})\b',
            # 美式日期格式
            r'\b(\d{1,2}/\d{1,2}/\d{4})\b',
            # 英式日期格式
            r'\b(\d{1,2}-\d{1,2}-\d{4})\b',
            # 中文日期格式
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
            # 月份名称格式
            r'\b((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4})\b',
            # 发布时间相关的模式
            r'(?:发布时间|发表时间|更新时间|时间)[:：]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'(?:Published|Updated|Created)[:：]\s*(\d{4}-\d{2}-\d{2})',
        ]

        for pattern in date_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                # 取第一个匹配的日期
                time_str = matches[0]
                normalized_time = self.normalize_time_format(time_str)
                if normalized_time:
                    return normalized_time

        return None

    def extract_site_specific(self, soup: BeautifulSoup, url: str) -> Optional[str]:
        """网站特定的时间提取规则"""
        domain = urlparse(url).netloc.lower()

        # 日本防卫省网站
        if 'mod.go.jp' in domain:
            return self.extract_mod_go_jp(soup, url)

        # 美军网站
        elif any(mil_domain in domain for mil_domain in ['.mil']):
            return self.extract_military_sites(soup)

        # 中文新闻网站
        elif any(chinese_domain in domain for chinese_domain in ['huaxia.com', 'ltn.com.tw', 'rfi.fr']):
            return self.extract_chinese_news_sites(soup)

        # Reuters
        elif 'reuters.com' in domain:
            return self.extract_reuters(soup)

        # BBC
        elif 'bbc.com' in domain:
            return self.extract_bbc(soup)

        return None

    def extract_mod_go_jp(self, soup: BeautifulSoup, url: str) -> Optional[str]:
        """日本防卫省网站特定提取"""
        # 从URL中提取日期（PDF文件名通常包含日期）
        if '.pdf' in url:
            # 匹配URL中的日期模式，如 20210827
            date_match = re.search(r'(\d{8})', url)
            if date_match:
                date_str = date_match.group(1)
                try:
                    # 转换为标准格式
                    date_obj = datetime.strptime(date_str, '%Y%m%d')
                    return date_obj.strftime('%Y-%m-%d 00:00:00')
                except:
                    pass

        # 查找页面中的日期信息
        date_elements = soup.find_all(['div', 'span', 'p'], class_=re.compile(r'date|time', re.I))
        for element in date_elements:
            if element.text:
                normalized_time = self.normalize_time_format(element.text.strip())
                if normalized_time:
                    return normalized_time

        return None

    def extract_military_sites(self, soup: BeautifulSoup) -> Optional[str]:
        """军方网站特定提取"""
        # 查找常见的军方网站日期格式
        selectors = [
            '.date',
            '.publish-date',
            '.article-date',
            '.news-date',
            '[class*="date"]',
            '[class*="time"]'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                if element.text:
                    normalized_time = self.normalize_time_format(element.text.strip())
                    if normalized_time:
                        return normalized_time

        return None

    def extract_chinese_news_sites(self, soup: BeautifulSoup) -> Optional[str]:
        """中文新闻网站特定提取"""
        # 查找中文日期格式
        chinese_date_selectors = [
            '.time',
            '.date',
            '.publish-time',
            '.article-time',
            '[class*="时间"]',
            '[class*="日期"]'
        ]

        for selector in chinese_date_selectors:
            elements = soup.select(selector)
            for element in elements:
                if element.text:
                    text = element.text.strip()
                    # 处理中文日期格式
                    normalized_time = self.normalize_chinese_time(text)
                    if normalized_time:
                        return normalized_time

        return None

    def extract_reuters(self, soup: BeautifulSoup) -> Optional[str]:
        """Reuters网站特定提取"""
        # Reuters特定的时间选择器
        selectors = [
            '[data-testid="ArticleHeader:date"]',
            '.ArticleHeader_date',
            '.DateTimeStamp_date'
        ]

        for selector in selectors:
            element = soup.select_one(selector)
            if element and element.text:
                normalized_time = self.normalize_time_format(element.text.strip())
                if normalized_time:
                    return normalized_time

        return None

    def extract_bbc(self, soup: BeautifulSoup) -> Optional[str]:
        """BBC网站特定提取"""
        # BBC特定的时间选择器
        selectors = [
            '[data-testid="timestamp"]',
            '.date',
            '.timestamp'
        ]

        for selector in selectors:
            element = soup.select_one(selector)
            if element and element.text:
                normalized_time = self.normalize_time_format(element.text.strip())
                if normalized_time:
                    return normalized_time

        return None

    def normalize_time_format(self, time_str: str) -> Optional[str]:
        """标准化时间格式"""
        if not time_str or time_str.strip() == '':
            return None

        time_str = time_str.strip()

        try:
            # 尝试使用dateutil解析
            from dateutil import parser as date_parser
            parsed_date = date_parser.parse(time_str, fuzzy=True)

            # 如果只有日期，设置时间为00:00:00
            if parsed_date.hour == 0 and parsed_date.minute == 0 and parsed_date.second == 0:
                return parsed_date.strftime('%Y-%m-%d 00:00:00')
            else:
                return parsed_date.strftime('%Y-%m-%d %H:%M:%S')

        except:
            # 如果dateutil失败，尝试手动解析
            return self.manual_time_parse(time_str)

    def normalize_chinese_time(self, time_str: str) -> Optional[str]:
        """标准化中文时间格式"""
        if not time_str:
            return None

        # 中文日期格式转换
        chinese_patterns = [
            (r'(\d{4})年(\d{1,2})月(\d{1,2})日', r'\1-\2-\3'),
            (r'(\d{4})/(\d{1,2})/(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})\.(\d{1,2})\.(\d{1,2})', r'\1-\2-\3'),
        ]

        for pattern, replacement in chinese_patterns:
            match = re.search(pattern, time_str)
            if match:
                try:
                    date_str = re.sub(pattern, replacement, time_str)
                    # 补齐月份和日期的零
                    parts = date_str.split('-')
                    if len(parts) == 3:
                        year, month, day = parts
                        formatted_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                        return f"{formatted_date} 00:00:00"
                except:
                    continue

        # 如果中文格式解析失败，尝试标准解析
        return self.normalize_time_format(time_str)

    def manual_time_parse(self, time_str: str) -> Optional[str]:
        """手动解析时间格式"""
        # 常见的时间格式模式
        patterns = [
            # ISO 8601
            (r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})', '%Y-%m-%dT%H:%M:%S'),
            # 标准格式
            (r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', '%Y-%m-%d %H:%M:%S'),
            (r'(\d{4}-\d{2}-\d{2})', '%Y-%m-%d'),
            # 美式格式
            (r'(\d{1,2}/\d{1,2}/\d{4})', '%m/%d/%Y'),
            # 英式格式
            (r'(\d{1,2}-\d{1,2}-\d{4})', '%d-%m-%Y'),
        ]

        for pattern, fmt in patterns:
            match = re.search(pattern, time_str)
            if match:
                try:
                    date_obj = datetime.strptime(match.group(1), fmt)
                    if '%H' not in fmt:  # 如果没有时间部分，添加00:00:00
                        return date_obj.strftime('%Y-%m-%d 00:00:00')
                    else:
                        return date_obj.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    continue

        return None

    def extract_time_from_url(self, url: str) -> Optional[str]:
        """从单个URL提取时间"""
        try:
            logger.info(f"正在处理URL: {url}")

            # 发送请求
            response = self.make_request(url)
            if not response:
                return None

            # 检查是否是PDF文件
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' in content_type or url.lower().endswith('.pdf'):
                return self.extract_time_from_pdf_url(url)

            # 处理HTML内容
            html_content = response.text
            extracted_time = self.extract_time_from_html(html_content, url)

            if extracted_time:
                logger.info(f"成功提取时间: {extracted_time}")
                return extracted_time
            else:
                logger.warning(f"未能提取到时间: {url}")
                return None

        except Exception as e:
            logger.error(f"处理URL时发生错误 {url}: {e}")
            return None

    def extract_time_from_pdf_url(self, url: str) -> Optional[str]:
        """从PDF URL提取时间"""
        # 首先尝试从URL文件名中提取日期
        date_match = re.search(r'(\d{8})', url)
        if date_match:
            date_str = date_match.group(1)
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                return date_obj.strftime('%Y-%m-%d 00:00:00')
            except:
                pass

        # 尝试从文件名中提取其他日期格式
        date_patterns = [
            r'(\d{4}-\d{2}-\d{2})',
            r'(\d{4}_\d{2}_\d{2})',
            r'(\d{2}-\d{2}-\d{4})',
        ]

        for pattern in date_patterns:
            match = re.search(pattern, url)
            if match:
                date_str = match.group(1)
                normalized_time = self.normalize_time_format(date_str)
                if normalized_time:
                    return normalized_time

        return None

    def process_record(self, record_id: str, data_sources: str) -> Tuple[str, str]:
        """处理单条记录"""
        urls = self.parse_multiple_urls(data_sources)

        if not urls:
            return record_id, "无法获取"

        # 尝试从每个URL提取时间
        for url in urls:
            extracted_time = self.extract_time_from_url(url)
            if extracted_time:
                self.success_count += 1
                return record_id, extracted_time

            # 在URL之间添加延迟
            time.sleep(random.uniform(1, 3))

        # 所有URL都失败
        self.failure_count += 1
        return record_id, "无法获取"

    def process_failed_records(self, csv_file_path: str, output_file_path: str = None):
        """处理失败记录文件"""
        if not output_file_path:
            output_file_path = csv_file_path.replace('_failed.csv', '_retry_results.csv')

        logger.info(f"开始处理文件: {csv_file_path}")

        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
            logger.info(f"共有 {len(df)} 条失败记录需要重新处理")

            # 处理每条记录
            results = []
            for index, row in df.iterrows():
                record_id = row['id']
                data_sources = str(row['data_sources'])

                logger.info(f"处理记录 {index + 1}/{len(df)}: {record_id}")

                # 处理记录
                _, extracted_time = self.process_record(record_id, data_sources)

                # 更新记录
                row['publish_time'] = extracted_time
                results.append(row)

                # 添加延迟避免被封
                time.sleep(random.uniform(2, 5))

                # 每处理10条记录显示进度
                if (index + 1) % 10 == 0:
                    logger.info(f"已处理 {index + 1}/{len(df)} 条记录，成功: {self.success_count}, 失败: {self.failure_count}")

            # 保存结果
            result_df = pd.DataFrame(results)
            result_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')

            logger.info(f"处理完成！结果已保存到: {output_file_path}")
            logger.info(f"总计: {len(df)} 条记录，成功: {self.success_count}, 失败: {self.failure_count}")
            logger.info(f"成功率: {self.success_count / len(df) * 100:.1f}%")

            return result_df

        except Exception as e:
            logger.error(f"处理文件时发生错误: {e}")
            return None

def main():
    """主函数 - 测试功能"""
    extractor = EnhancedTimeExtractor()

    # 测试单个URL
    test_urls = [
        "https://www.reuters.com/world/asia-pacific/south-korean-marines-stage-amphibious-landing-exercise-2022-10-26/",
        "https://www.mod.go.jp/msdf/en/release/202108/20210827-2.pdf"
    ]

    print("=== 测试单个URL提取 ===")
    for url in test_urls:
        result = extractor.extract_time_from_url(url)
        print(f"URL: {url}")
        print(f"提取结果: {result}")
        print("-" * 50)

if __name__ == "__main__":
    main()
